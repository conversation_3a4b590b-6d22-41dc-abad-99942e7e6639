"""
演示脚本 - 快速演示对抗训练系统
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from config import Config
from adversarial_trainer import AdversarialTrainer
from evaluation_tasks import run_comprehensive_evaluation

def quick_demo():
    """快速演示函数"""
    print("=== 对抗环境-智能体系统演示 ===\n")
    
    # 创建配置（使用较小的参数进行快速演示）
    config = Config()
    config.total_episodes = 1000
    config.eval_frequency = 200
    config.adversarial_start = 300
    config.batch_size = 32
    
    print(f"使用设备: {config.device}")
    print(f"总训练轮数: {config.total_episodes}")
    print(f"对抗训练开始轮数: {config.adversarial_start}\n")
    
    # 创建训练器
    trainer = AdversarialTrainer(config)
    
    # 生成演示数据
    def generate_demo_batch():
        return {
            'x_t': torch.randn(config.batch_size, config.state_dim, device=config.device) * 0.5,
            'y_t': torch.randn(config.batch_size, config.state_dim, device=config.device) * 0.1,
            'c_t': torch.randn(config.batch_size, config.context_dim, device=config.device) * 0.3,
            'a_t': torch.randn(config.batch_size, config.action_dim, device=config.device) * 0.2
        }
    
    # 训练循环
    print("开始训练...")
    agent_losses = []
    env_losses = []
    
    for episode in range(config.total_episodes):
        trainer.episode_count = episode
        
        # 生成数据并训练
        batch_data = generate_demo_batch()
        metrics = trainer.train_step(batch_data)
        
        agent_losses.append(metrics['total_agent_loss'])
        env_losses.append(metrics['total_env_loss'])
        
        # 打印进度
        if episode % 100 == 0:
            mode = "对抗模式" if episode >= config.adversarial_start else "预热模式"
            print(f"Episode {episode}: Agent Loss={metrics['total_agent_loss']:.4f}, "
                  f"Env Loss={metrics['total_env_loss']:.4f} ({mode})")
        
        # 定期评估
        if episode % config.eval_frequency == 0 and episode > 0:
            print(f"\n--- 第 {episode} 轮评估 ---")
            eval_results = run_comprehensive_evaluation(trainer.agent_net, trainer.env_net, config)
            print(f"趋近点任务成功率: {eval_results['point_approach']['success_rates']}")
            print(f"稀疏奖励任务成功率: {eval_results['sparse_reward']['success_rate']:.3f}")
            print(f"综合得分: {eval_results['overall_score']:.3f}")
            print("-" * 40)
    
    print("\n训练完成！")
    
    # 最终评估
    print("\n=== 最终评估 ===")
    final_results = run_comprehensive_evaluation(trainer.agent_net, trainer.env_net, config)
    
    print(f"趋近点任务最终结果:")
    for difficulty, success_rate in final_results['point_approach']['success_rates'].items():
        print(f"  {difficulty}: {success_rate:.3f}")
    
    print(f"稀疏奖励任务成功率: {final_results['sparse_reward']['success_rate']:.3f}")
    print(f"最终综合得分: {final_results['overall_score']:.3f}")
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(agent_losses, label='Agent Loss', alpha=0.7)
    plt.axvline(x=config.adversarial_start, color='red', linestyle='--', 
                label='对抗训练开始', alpha=0.7)
    plt.title('智能体损失曲线')
    plt.xlabel('Episode')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.plot(env_losses, label='Environment Loss', color='orange', alpha=0.7)
    plt.axvline(x=config.adversarial_start, color='red', linestyle='--', 
                label='对抗训练开始', alpha=0.7)
    plt.title('环境损失曲线')
    plt.xlabel('Episode')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('demo_training_curves.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("\n演示完成！训练曲线已保存为 'demo_training_curves.png'")
    
    return final_results

def analyze_adversarial_dynamics():
    """分析对抗动态"""
    print("\n=== 对抗动态分析 ===")
    
    config = Config()
    trainer = AdversarialTrainer(config)
    
    # 生成测试数据
    test_data = {
        'x_t': torch.randn(10, config.state_dim, device=config.device) * 0.5,
        'y_t': torch.randn(10, config.state_dim, device=config.device) * 0.1,
        'c_t': torch.randn(10, config.context_dim, device=config.device) * 0.3,
        'a_t': torch.randn(10, config.action_dim, device=config.device) * 0.2
    }
    
    # 分析信息交换
    x_t_sub, c_t_sub = trainer.extract_subsets(test_data['x_t'], test_data['c_t'])
    
    print(f"原始状态维度: {test_data['x_t'].shape}")
    print(f"智能体向环境传递的信息维度: {x_t_sub.shape}")
    print(f"原始上下文维度: {test_data['c_t'].shape}")
    print(f"环境向智能体传递的信息维度: {c_t_sub.shape}")
    
    # 分析网络输出
    with torch.no_grad():
        c_next, a_next, physics_score = trainer.env_net(
            test_data['c_t'], test_data['a_t'], x_t_sub)
        x_next, y_next = trainer.agent_net(
            test_data['x_t'], test_data['y_t'], c_t_sub)
    
    print(f"\n环境网络输出:")
    print(f"  下一步上下文范围: [{c_next.min():.3f}, {c_next.max():.3f}]")
    print(f"  下一步动作范围: [{a_next.min():.3f}, {a_next.max():.3f}]")
    print(f"  物理合理性分数: {physics_score.mean():.3f} ± {physics_score.std():.3f}")
    
    print(f"\n智能体网络输出:")
    print(f"  下一步状态范围: [{x_next.min():.3f}, {x_next.max():.3f}]")
    print(f"  下一步输出范围: [{y_next.min():.3f}, {y_next.max():.3f}]")
    
    return {
        'info_exchange': {
            'x_sub_ratio': x_t_sub.shape[1] / test_data['x_t'].shape[1],
            'c_sub_ratio': c_t_sub.shape[1] / test_data['c_t'].shape[1]
        },
        'network_outputs': {
            'env_context_range': [c_next.min().item(), c_next.max().item()],
            'env_action_range': [a_next.min().item(), a_next.max().item()],
            'physics_score': physics_score.mean().item(),
            'agent_state_range': [x_next.min().item(), x_next.max().item()],
            'agent_output_range': [y_next.min().item(), y_next.max().item()]
        }
    }

if __name__ == "__main__":
    # 运行演示
    demo_results = quick_demo()
    
    # 分析对抗动态
    dynamics_analysis = analyze_adversarial_dynamics()
    
    print(f"\n=== 演示总结 ===")
    print(f"最终综合得分: {demo_results['overall_score']:.3f}")
    print(f"信息交换比例: 智能体→环境 {dynamics_analysis['info_exchange']['x_sub_ratio']:.1%}, "
          f"环境→智能体 {dynamics_analysis['info_exchange']['c_sub_ratio']:.1%}")
    print("演示完成！")
