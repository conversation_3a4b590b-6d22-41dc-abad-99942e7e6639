"""
评估任务 - 实现趋近点和稀疏奖励两类测试任务
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from config import Config

class PointApproachTask:
    """趋近点任务 - 测试智能体是否能够趋近指定目标点"""
    
    def __init__(self, config):
        self.config = config
        self.device = config.device
        
    def generate_targets(self, batch_size, difficulty='medium'):
        """
        生成目标点
        Args:
            batch_size: 批次大小
            difficulty: 难度级别 ('easy', 'medium', 'hard')
        Returns:
            targets: 目标点 [batch_size, state_dim]
        """
        if difficulty == 'easy':
            # 简单：目标点在原点附近
            targets = torch.randn(batch_size, self.config.state_dim, device=self.device) * 0.5
        elif difficulty == 'medium':
            # 中等：目标点在中等距离
            targets = torch.randn(batch_size, self.config.state_dim, device=self.device) * 1.0
        else:  # hard
            # 困难：目标点距离较远
            targets = torch.randn(batch_size, self.config.state_dim, device=self.device) * 2.0
            
        return targets
    
    def evaluate(self, agent_net, env_net, num_episodes=100, max_steps=50):
        """
        评估智能体在趋近点任务上的表现
        Args:
            agent_net: 智能体网络
            env_net: 环境网络
            num_episodes: 评估轮数
            max_steps: 每轮最大步数
        Returns:
            results: 评估结果字典
        """
        agent_net.eval()
        env_net.eval()
        
        success_rates = {'easy': 0, 'medium': 0, 'hard': 0}
        avg_distances = {'easy': [], 'medium': [], 'hard': []}
        convergence_steps = {'easy': [], 'medium': [], 'hard': []}
        
        with torch.no_grad():
            for difficulty in ['easy', 'medium', 'hard']:
                successes = 0
                
                for episode in range(num_episodes):
                    # 初始化状态
                    x_t = torch.randn(1, self.config.state_dim, device=self.device) * 0.1
                    y_t = torch.zeros(1, self.config.state_dim, device=self.device)
                    c_t = torch.randn(1, self.config.context_dim, device=self.device) * 0.1
                    a_t = torch.zeros(1, self.config.action_dim, device=self.device)
                    
                    # 生成目标
                    target = self.generate_targets(1, difficulty)
                    
                    # 模拟轨迹
                    distances = []
                    converged_step = max_steps
                    
                    for step in range(max_steps):
                        # 提取子集
                        sub_x_dim = int(self.config.state_dim * self.config.sub_x_ratio)
                        sub_c_dim = int(self.config.context_dim * self.config.sub_c_ratio)
                        
                        x_indices = torch.randperm(self.config.state_dim)[:sub_x_dim]
                        c_indices = torch.randperm(self.config.context_dim)[:sub_c_dim]
                        
                        x_t_sub = x_t[:, x_indices]
                        c_t_sub = c_t[:, c_indices]
                        
                        # 环境更新
                        c_next, a_next, _ = env_net(c_t, a_t, x_t_sub)
                        
                        # 智能体更新（添加目标引导）
                        target_guidance = (target - x_t) * 0.1  # 轻微的目标引导
                        x_next, y_next = agent_net(x_t + target_guidance, y_t, c_t_sub)
                        
                        # 计算距离
                        distance = torch.norm(x_next - target).item()
                        distances.append(distance)
                        
                        # 检查是否收敛
                        if distance < self.config.target_tolerance:
                            if converged_step == max_steps:
                                converged_step = step
                            if step >= converged_step + 5:  # 稳定收敛5步
                                successes += 1
                                break
                        
                        # 更新状态
                        x_t, y_t, c_t, a_t = x_next, y_next, c_next, a_next
                    
                    avg_distances[difficulty].append(min(distances))
                    if converged_step < max_steps:
                        convergence_steps[difficulty].append(converged_step)
                
                success_rates[difficulty] = successes / num_episodes
        
        # 计算统计结果
        results = {
            'success_rates': success_rates,
            'avg_final_distances': {k: np.mean(v) if v else float('inf') 
                                  for k, v in avg_distances.items()},
            'avg_convergence_steps': {k: np.mean(v) if v else float('inf') 
                                    for k, v in convergence_steps.items()},
            'task_type': 'point_approach'
        }
        
        agent_net.train()
        env_net.train()
        
        return results


class SparseRewardTask:
    """稀疏奖励任务 - 测试智能体在稀疏奖励环境中的学习能力"""
    
    def __init__(self, config):
        self.config = config
        self.device = config.device
        
    def compute_sparse_reward(self, x_t, y_t):
        """
        计算稀疏奖励
        Args:
            x_t: 当前状态
            y_t: 当前输出
        Returns:
            reward: 稀疏奖励
        """
        # 奖励条件：状态和输出的某种特定组合
        # 例如：当状态的前半部分接近0，后半部分接近1时给予奖励
        state_dim = x_t.size(-1)
        half_dim = state_dim // 2
        
        front_half = x_t[:, :half_dim]
        back_half = x_t[:, half_dim:]
        
        # 前半部分接近0的程度
        front_score = torch.exp(-torch.mean(front_half ** 2, dim=-1))
        
        # 后半部分接近1的程度
        back_score = torch.exp(-torch.mean((back_half - 1) ** 2, dim=-1))
        
        # 输出稳定性
        output_stability = torch.exp(-torch.mean((y_t - x_t) ** 2, dim=-1))
        
        # 综合分数
        total_score = front_score * back_score * output_stability
        
        # 稀疏奖励：只有当分数超过阈值时才给奖励
        reward = torch.where(total_score > self.config.sparse_reward_threshold, 
                           total_score, torch.zeros_like(total_score))
        
        return reward, total_score
    
    def evaluate(self, agent_net, env_net, num_episodes=100, max_steps=100):
        """
        评估智能体在稀疏奖励任务上的表现
        Args:
            agent_net: 智能体网络
            env_net: 环境网络
            num_episodes: 评估轮数
            max_steps: 每轮最大步数
        Returns:
            results: 评估结果字典
        """
        agent_net.eval()
        env_net.eval()
        
        total_rewards = []
        success_episodes = 0
        reward_discovery_steps = []
        
        with torch.no_grad():
            for episode in range(num_episodes):
                # 初始化状态
                x_t = torch.randn(1, self.config.state_dim, device=self.device) * 0.5
                y_t = torch.randn(1, self.config.state_dim, device=self.device) * 0.1
                c_t = torch.randn(1, self.config.context_dim, device=self.device) * 0.1
                a_t = torch.zeros(1, self.config.action_dim, device=self.device)
                
                episode_rewards = []
                first_reward_step = max_steps
                
                for step in range(max_steps):
                    # 提取子集
                    sub_x_dim = int(self.config.state_dim * self.config.sub_x_ratio)
                    sub_c_dim = int(self.config.context_dim * self.config.sub_c_ratio)
                    
                    x_indices = torch.randperm(self.config.state_dim)[:sub_x_dim]
                    c_indices = torch.randperm(self.config.context_dim)[:sub_c_dim]
                    
                    x_t_sub = x_t[:, x_indices]
                    c_t_sub = c_t[:, c_indices]
                    
                    # 环境更新
                    c_next, a_next, _ = env_net(c_t, a_t, x_t_sub)
                    
                    # 智能体更新
                    x_next, y_next = agent_net(x_t, y_t, c_t_sub)
                    
                    # 计算奖励
                    reward, score = self.compute_sparse_reward(x_next, y_next)
                    episode_rewards.append(reward.item())
                    
                    # 记录首次获得奖励的步数
                    if reward.item() > 0 and first_reward_step == max_steps:
                        first_reward_step = step
                    
                    # 更新状态
                    x_t, y_t, c_t, a_t = x_next, y_next, c_next, a_next
                
                total_reward = sum(episode_rewards)
                total_rewards.append(total_reward)
                
                if total_reward > 0:
                    success_episodes += 1
                    reward_discovery_steps.append(first_reward_step)
        
        # 计算统计结果
        results = {
            'success_rate': success_episodes / num_episodes,
            'avg_total_reward': np.mean(total_rewards),
            'max_total_reward': np.max(total_rewards),
            'avg_discovery_steps': np.mean(reward_discovery_steps) if reward_discovery_steps else float('inf'),
            'reward_variance': np.var(total_rewards),
            'task_type': 'sparse_reward'
        }
        
        agent_net.train()
        env_net.train()
        
        return results


def run_comprehensive_evaluation(agent_net, env_net, config):
    """
    运行综合评估
    Args:
        agent_net: 智能体网络
        env_net: 环境网络
        config: 配置对象
    Returns:
        results: 综合评估结果
    """
    print("开始综合评估...")
    
    # 趋近点任务评估
    point_task = PointApproachTask(config)
    point_results = point_task.evaluate(agent_net, env_net)
    
    # 稀疏奖励任务评估
    sparse_task = SparseRewardTask(config)
    sparse_results = sparse_task.evaluate(agent_net, env_net)
    
    # 综合结果
    comprehensive_results = {
        'point_approach': point_results,
        'sparse_reward': sparse_results,
        'overall_score': (
            np.mean(list(point_results['success_rates'].values())) + 
            sparse_results['success_rate']
        ) / 2
    }
    
    print(f"评估完成！综合得分: {comprehensive_results['overall_score']:.3f}")
    
    return comprehensive_results
