"""
对抗训练器 - 实现环境和智能体的对抗训练逻辑
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from collections import deque
import logging
from networks import create_networks
from config import Config

class AdversarialTrainer:
    """对抗训练器类"""
    
    def __init__(self, config):
        self.config = config
        self.device = config.device
        
        # 创建网络
        self.env_net, self.agent_net = create_networks(config)
        
        # 创建优化器
        self.env_optimizer = optim.Adam(self.env_net.parameters(), lr=config.learning_rate_E)
        self.agent_optimizer = optim.Adam(self.agent_net.parameters(), lr=config.learning_rate_A)
        
        # 学习率调度器
        self.env_scheduler = optim.lr_scheduler.StepLR(self.env_optimizer, step_size=2000, gamma=0.8)
        self.agent_scheduler = optim.lr_scheduler.StepLR(self.agent_optimizer, step_size=2000, gamma=0.8)
        
        # 历史状态缓冲区（用于多样性计算）
        self.history_buffer = deque(maxlen=100)
        
        # 训练统计
        self.episode_count = 0
        self.agent_losses = []
        self.env_losses = []
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def extract_subsets(self, x_t, c_t):
        """
        提取用于信息交换的子集
        Args:
            x_t: 智能体状态 [batch_size, state_dim]
            c_t: 环境上下文 [batch_size, context_dim]
        Returns:
            x_t_sub: 智能体状态子集
            c_t_sub: 环境上下文子集
        """
        batch_size = x_t.size(0)
        
        # 随机选择子集索引（每个batch可以不同）
        sub_x_dim = int(self.config.state_dim * self.config.sub_x_ratio)
        sub_c_dim = int(self.config.context_dim * self.config.sub_c_ratio)
        
        # 为了训练稳定性，使用固定的子集选择策略
        x_indices = torch.randperm(self.config.state_dim)[:sub_x_dim]
        c_indices = torch.randperm(self.config.context_dim)[:sub_c_dim]
        
        x_t_sub = x_t[:, x_indices]
        c_t_sub = c_t[:, c_indices]
        
        return x_t_sub, c_t_sub
    
    def compute_agent_loss(self, x_t, y_t, x_target=None):
        """
        计算智能体损失
        Args:
            x_t: 当前状态
            y_t: 当前输出
            x_target: 目标状态（如果有的话）
        Returns:
            loss: 智能体损失
            metrics: 训练指标字典
        """
        # 基础损失：让特定维度趋向于0（稳态要求）
        sub_loss_indices = torch.randperm(self.config.state_dim)[:self.config.state_dim//4]
        stability_loss = torch.mean(x_t[:, sub_loss_indices] ** 2)
        
        # 如果有目标，添加目标损失
        target_loss = 0.0
        if x_target is not None:
            target_loss = torch.mean((x_t - x_target) ** 2)
        
        # 输出平滑性损失
        smoothness_loss = torch.mean((y_t - x_t) ** 2) * 0.1
        
        total_loss = stability_loss + target_loss + smoothness_loss
        
        metrics = {
            'stability_loss': stability_loss.item(),
            'target_loss': target_loss if isinstance(target_loss, float) else target_loss.item(),
            'smoothness_loss': smoothness_loss.item(),
            'total_agent_loss': total_loss.item()
        }
        
        return total_loss, metrics
    
    def compute_environment_loss(self, agent_loss, c_next, a_next, physics_score):
        """
        计算环境损失（对抗损失）
        Args:
            agent_loss: 智能体损失
            c_next: 环境预测的下一步上下文
            a_next: 环境预测的下一步动作
            physics_score: 物理合理性分数
        Returns:
            loss: 环境损失
            metrics: 训练指标字典
        """
        # 对抗损失：最大化智能体损失
        adversarial_loss = -self.config.alpha * agent_loss
        
        # 物理约束损失：鼓励物理合理的状态
        physics_loss = -torch.mean(physics_score) * self.config.beta
        
        # 多样性损失：鼓励生成多样化的状态
        diversity_loss = self.env_net.compute_diversity_loss(c_next, a_next, self.history_buffer)
        diversity_loss = diversity_loss * self.config.gamma
        
        # 稳定性正则化：防止环境变化过于剧烈
        stability_reg = torch.mean(torch.norm(c_next, dim=-1)) * 0.01
        action_reg = torch.mean(torch.norm(a_next, dim=-1)) * 0.01
        
        total_loss = adversarial_loss + physics_loss + diversity_loss + stability_reg + action_reg
        
        metrics = {
            'adversarial_loss': adversarial_loss.item(),
            'physics_loss': physics_loss.item(),
            'diversity_loss': diversity_loss.item(),
            'stability_reg': stability_reg.item(),
            'action_reg': action_reg.item(),
            'total_env_loss': total_loss.item()
        }
        
        return total_loss, metrics
    
    def train_step(self, batch_data, x_target=None):
        """
        执行一步对抗训练
        Args:
            batch_data: 批次数据字典
            x_target: 目标状态（可选）
        Returns:
            metrics: 训练指标字典
        """
        # 解包数据
        x_t = batch_data['x_t'].to(self.device)
        y_t = batch_data['y_t'].to(self.device)
        c_t = batch_data['c_t'].to(self.device)
        a_t = batch_data['a_t'].to(self.device)
        
        if x_target is not None:
            x_target = x_target.to(self.device)
        
        # 提取信息交换子集
        x_t_sub, c_t_sub = self.extract_subsets(x_t, c_t)
        
        # 前向传播
        # 环境网络预测
        c_next, a_next, physics_score = self.env_net(c_t, a_t, x_t_sub)
        
        # 智能体网络预测
        x_next, y_next = self.agent_net(x_t, y_t, c_t_sub)
        
        # 计算损失
        agent_loss, agent_metrics = self.compute_agent_loss(x_next, y_next, x_target)
        env_loss, env_metrics = self.compute_environment_loss(agent_loss, c_next, a_next, physics_score)
        
        # 更新网络
        all_metrics = {}
        
        # 训练智能体
        self.agent_optimizer.zero_grad()
        agent_loss.backward(retain_graph=True)
        torch.nn.utils.clip_grad_norm_(self.agent_net.parameters(), max_norm=1.0)
        self.agent_optimizer.step()
        
        # 训练环境（如果已过预热期）
        if self.episode_count >= self.config.adversarial_start:
            self.env_optimizer.zero_grad()
            env_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.env_net.parameters(), max_norm=1.0)
            self.env_optimizer.step()
        
        # 更新历史缓冲区
        current_state = torch.cat([c_next.detach(), a_next.detach()], dim=-1)
        self.history_buffer.append(current_state.mean(dim=0))  # 存储批次平均
        
        # 合并指标
        all_metrics.update(agent_metrics)
        all_metrics.update(env_metrics)
        all_metrics['episode'] = self.episode_count
        
        return all_metrics
